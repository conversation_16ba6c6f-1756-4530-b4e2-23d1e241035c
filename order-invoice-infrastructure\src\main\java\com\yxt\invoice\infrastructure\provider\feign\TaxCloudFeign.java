package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.provider.dto.req.GetInvoiceByResponseIdReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PositiveInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PostNegativeInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.res.GetInvoiceByResponseIdResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PositiveInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PostNegativeInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.TaxCloudResponse;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 税务云接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(name = "tax-cloud-service", url = "${remote.tax-cloud.url}")
public interface TaxCloudFeign {

//    @PostMapping("/positiveInvoiceIssue") // 文档上是这个名字. 改用下面的地址
    @PostMapping("/positiveInvoiceBatch")
    TaxCloudResponse<List<PositiveInvoiceIssueResDto>> positiveInvoiceBatch(@RequestBody PositiveInvoiceIssueReqDto req);


    @PostMapping("/postNegativeInvoiceIssue")
    TaxCloudResponse<PostNegativeInvoiceIssueResDto> postNegativeInvoiceIssue(@RequestBody PostNegativeInvoiceIssueReqDto req);


//    @PostMapping("/getInvoiceByResponseId") // 文档上是这个名字. 改用下面的地址
    @PostMapping("/getInvoiceByOutRequestCode")
    TaxCloudResponse<GetInvoiceByResponseIdResDto> getInvoiceByOutRequestCode(@RequestBody GetInvoiceByResponseIdReqDto req);


}
