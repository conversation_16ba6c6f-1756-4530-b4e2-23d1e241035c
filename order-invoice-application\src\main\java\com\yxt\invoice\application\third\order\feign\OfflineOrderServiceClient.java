package com.yxt.invoice.application.third.order.feign;

import com.yxt.order.open.sdk.OrderServiceName;
import com.yxt.order.open.sdk.offline_order.OfflineOrderManageQueryApi;
import com.yxt.order.open.sdk.offline_order.OfflineOrderQueryApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 线下单接口
 */
@FeignClient(value = OrderServiceName.value)
public interface OfflineOrderServiceClient extends OfflineOrderManageQueryApi, OfflineOrderQueryApi {


}
