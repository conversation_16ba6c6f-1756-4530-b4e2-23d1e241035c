package com.yxt.invoice.application.third.order.feign;

import com.yxt.domain.order.BusinessOrderServiceName;
import com.yxt.domain.order.order_query.OrderQueryDomainApi;
import com.yxt.domain.order.refund_query.RefundQueryDomainApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * O2O接口
 */
@FeignClient(value = BusinessOrderServiceName.value)
public interface O2OOrderClient extends RefundQueryDomainApi, OrderQueryDomainApi {


}
