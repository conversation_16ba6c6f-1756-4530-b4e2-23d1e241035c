package com.yxt.invoice.application.service.convert;


import com.google.common.base.Preconditions;

import com.yxt.domain.order.order_query.res.B2cOmsOrderInfoResDto;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.B2cRefundSearchByRefundNoRes;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.order.common.base_order_dto.OrderDetail;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.common.base_order_dto.RefundDetail;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.types.invoice.enums.*;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class InvoiceConvert {


    public static ApplyInvoiceCommand applyInvoiceConvertByOfflineOrder(OfflineOrderModel data, List<OfflineRefundOrderModel> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        OfflineOrderModel.OfflineOrderInfo baseOrderInfo = data.getBaseOrderInfo();
        OfflineOrderModel.Organization baseOrganizationInfo = data.getBaseOrganizationInfo();
        LocalDateTime applyTime = command.getInvoiceMain().getApplyTime();
        String  createdBy = command.getInvoiceMain().getCreatedBy();
        InvoiceMain invoiceMain = command.getInvoiceMain();

        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;



        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOfflineOrder(data,refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
        BigDecimal invoiceAmount = new BigDecimal(first.get().getAmount());
        BigDecimal actualPayAmount =invoiceAmount;

        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
        Map<String, List<OfflineRefundOrderModel.RefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(OfflineRefundOrderModel.RefundDetail::getRowNo));


        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
        for (OfflineOrderModel.OrderDetail orderDetail : data.getOrderDetailList()) {
            BigDecimal commodityCount = orderDetail.getBaseOrderDetailInfo().getCommodityCount();
            String rowNo = orderDetail.getBaseOrderDetailInfo().getRowNo();
            List<OfflineRefundOrderModel.RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
            if(!refundDetails.isEmpty()){
                for (OfflineRefundOrderModel.RefundDetail refundDetail : refundDetails) {
                    commodityCount=commodityCount.subtract(refundDetail.getRefundCount());
                }
            }
            if(commodityCount.intValue()<=0){
                continue;
            }
            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
            BigDecimal taxRate =new BigDecimal(averagePriceVO.getTaxRate()) ;
            BigDecimal price = orderDetail.getBaseOrderDetailInfo().getPrice();
            BigDecimal totalAmount=price.multiply(commodityCount);
            BigDecimal taxAmount=totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            BigDecimal PriceTaxAmount=totalAmount.add(taxAmount);

            sumDetailInvoiceAmount=sumDetailInvoiceAmount.add(totalAmount);
            sumDetailTaxAmount=sumDetailTaxAmount.add(taxAmount);



            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setRowNo(rowNo);
            invoiceDetail.setErpCode(orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
            invoiceDetail.setErpName(orderDetail.getBaseOrderDetailInfo().getErpName());
            invoiceDetail.setCommodityCount(commodityCount);
            invoiceDetail.setPrice(price);
            invoiceDetail.setTotalAmount(totalAmount);
            invoiceDetail.setTaxAmount(taxAmount);
            invoiceDetail.setTaxRate(taxRate);
            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
            invoiceDetail.setInvoiceLineType("Regular_Line");
            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
            invoiceDetail.setPolicyStatus("NO");
            invoiceDetail.setPolicyTaxRate(null);
            invoiceDetail.setIsValid(1L);
            invoiceDetail.setCreated(applyTime);
            invoiceDetail.setUpdated(applyTime);
            invoiceDetail.setCreatedBy(createdBy);
            invoiceDetail.setUpdatedBy(createdBy);
            invoiceDetail.setVersion(1L);
            invoiceDetails.add(invoiceDetail);
        }

        Preconditions.checkArgument(invoiceAmount.doubleValue()!=sumDetailInvoiceAmount.doubleValue(),"发票头与商品行合计金额不一致");
        BigDecimal priceTaxAmount=sumDetailInvoiceAmount.add(sumDetailTaxAmount);

        invoiceMain.setCompanyCode(baseOrganizationInfo.getCompanyCode());
        invoiceMain.setCompanyName(baseOrganizationInfo.getCompanyName());
        invoiceMain.setOrganizationCode(baseOrganizationInfo.getStoreCode());
        invoiceMain.setOrganizationName(baseOrganizationInfo.getStoreName());
        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCodeValue());
        invoiceMain.setThirdOrderNo(baseOrderInfo.getThirdOrderNo());
        invoiceMain.setOrderNo(baseOrderInfo.getOrderNo());
        invoiceMain.setPosNo(baseOrderInfo.getThirdOrderNo().getThirdOrderNo());
        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
        invoiceMain.setActualPayAmount(actualPayAmount);
        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
        invoiceMain.setDeliveryType("SelfPickup");
        invoiceMain.setInvoiceAmount(invoiceAmount);
        invoiceMain.setTaxAmount(sumDetailTaxAmount);
        invoiceMain.setPriceTaxAmount(priceTaxAmount);
        invoiceMain.setSplitBill("NOT");
        invoiceMain.setOrderCreated(Instant.ofEpochMilli(baseOrderInfo.getCreated().getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
        invoiceMain.setSellerNumber(baseOrganizationInfo.getStoreCode());
        invoiceMain.setSellerName(baseOrganizationInfo.getStoreName());
        invoiceMain.setIsValid(1L);
        invoiceMain.setCreated(LocalDateTime.now());
        invoiceMain.setUpdated(LocalDateTime.now());
        invoiceMain.setVersion(1L);

        command.setInvoiceMain(invoiceMain);
        command.setDetails(invoiceDetails);
        return command;
    }




    public static ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderO2O(OrderDomainRelatedRes data, List<RefundDomainRelatedRes> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        OrderInfo baseOrderInfo = data.getOrderInfo();
        String deliveryTypeTemp = data.getOrderDeliveryRecord().getDeliveryType();
        LocalDateTime applyTime = command.getInvoiceMain().getApplyTime();
        String  createdBy = command.getInvoiceMain().getCreatedBy();
        InvoiceMain invoiceMain = command.getInvoiceMain();
        String deliveryType;
        if (deliveryTypeTemp.equals("3")) {
            deliveryType = "SelfPickup";
        } else if(deliveryTypeTemp.equals("1")||deliveryTypeTemp.equals("2")) {
            deliveryType = "PlatformFulfillment";
        }else{
            deliveryType="MerchantFulfillment";

        }
        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOnlineOrderO2O(data,refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
        BigDecimal invoiceAmount = new BigDecimal(first.get().getAmount());

        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
        Map<String, List<RefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(RefundDetail::getThirdDetailId));


        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
        for (OrderDetail orderDetail : data.getDetailList()) {
            if(orderDetail.getStatus()!=0){
               continue;
            }
            Integer commodityCount = orderDetail.getCommodityCount();
            String rowNo = orderDetail.getThirdDetailId();
            List<RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
            if(!refundDetails.isEmpty()){
                for (RefundDetail refundDetail : refundDetails) {
                    commodityCount=commodityCount-(refundDetail.getRefundCount());
                }
            }
            if(commodityCount.intValue()<=0){
                continue;
            }
            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
            BigDecimal taxRate =new BigDecimal(averagePriceVO.getTaxRate()) ;
            BigDecimal price = orderDetail.getPrice();
            BigDecimal totalAmount=price.multiply(new BigDecimal(commodityCount));
            BigDecimal taxAmount=totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            BigDecimal PriceTaxAmount=totalAmount.add(taxAmount);

            sumDetailInvoiceAmount=sumDetailInvoiceAmount.add(totalAmount);
            sumDetailTaxAmount=sumDetailTaxAmount.add(taxAmount);


            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setRowNo(rowNo);
            invoiceDetail.setErpCode(orderDetail.getErpCode());
            invoiceDetail.setErpName(orderDetail.getCommodityName());
            invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
            invoiceDetail.setPrice(price);
            invoiceDetail.setTotalAmount(totalAmount);
            invoiceDetail.setTaxAmount(taxAmount);
            invoiceDetail.setTaxRate(taxRate);
            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
            invoiceDetail.setInvoiceLineType("Regular_Line");
            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
            invoiceDetail.setPolicyStatus("NO");
            invoiceDetail.setPolicyTaxRate(null);
            invoiceDetail.setIsValid(1L);
            invoiceDetail.setCreated(applyTime);
            invoiceDetail.setUpdated(applyTime);
            invoiceDetail.setCreatedBy(createdBy);
            invoiceDetail.setUpdatedBy(createdBy);
            invoiceDetail.setVersion(1L);
            invoiceDetails.add(invoiceDetail);
        }


        Preconditions.checkArgument(invoiceAmount.doubleValue()!=sumDetailInvoiceAmount.doubleValue(),"发票头与商品行合计金额不一致");
        BigDecimal priceTaxAmount=sumDetailInvoiceAmount.add(sumDetailTaxAmount);

        invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
        invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
        invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
        invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
        invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
        invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
        invoiceMain.setActualPayAmount(invoiceAmount);
        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
        invoiceMain.setDeliveryType(deliveryType);
        invoiceMain.setInvoiceAmount(invoiceAmount);
        invoiceMain.setTaxAmount(sumDetailTaxAmount);
        invoiceMain.setPriceTaxAmount(priceTaxAmount);
        invoiceMain.setSplitBill("NOT");
        invoiceMain.setOrderCreated(Instant.ofEpochMilli(baseOrderInfo.getCreated().getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
        invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
        invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
        invoiceMain.setIsValid(1L);
        invoiceMain.setCreated(LocalDateTime.now());
        invoiceMain.setUpdated(LocalDateTime.now());
        invoiceMain.setVersion(1L);

        command.setInvoiceMain(invoiceMain);
        command.setDetails(invoiceDetails);


        return command;
    }


    public static ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderB2C(B2cOmsOrderInfoResDto data, List<B2cRefundSearchByRefundNoRes> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        B2cOmsOrderInfoResDto baseOrderInfo = data;
        LocalDateTime applyTime = command.getInvoiceMain().getApplyTime();
        String  createdBy = command.getInvoiceMain().getCreatedBy();
        InvoiceMain invoiceMain = command.getInvoiceMain();
        String deliveryType="MerchantFulfillment";

        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOnlineOrderB2C(data,refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
//        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
//        BigDecimal invoiceAmount = new BigDecimal(first.get().getAmount());
//
//        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
//        Map<String, List<RefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(RefundDetail::getThirdDetailId));
//
//
//        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
//        for (OrderDetail orderDetail : data.getDetailList()) {
//            if(orderDetail.getStatus()!=0){
//                continue;
//            }
//            Integer commodityCount = orderDetail.getCommodityCount();
//            String rowNo = orderDetail.getThirdDetailId();
//            List<RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
//            if(!refundDetails.isEmpty()){
//                for (RefundDetail refundDetail : refundDetails) {
//                    commodityCount=commodityCount-(refundDetail.getRefundCount());
//                }
//            }
//            if(commodityCount.intValue()<=0){
//                continue;
//            }
//            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
//            BigDecimal taxRate =new BigDecimal(averagePriceVO.getTaxRate()) ;
//            BigDecimal price = orderDetail.getPrice();
//            BigDecimal totalAmount=price.multiply(new BigDecimal(commodityCount));
//            BigDecimal taxAmount=totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
//            BigDecimal PriceTaxAmount=totalAmount.add(taxAmount);
//
//            sumDetailInvoiceAmount=sumDetailInvoiceAmount.add(totalAmount);
//            sumDetailTaxAmount=sumDetailTaxAmount.add(taxAmount);
//
//
//            InvoiceDetail invoiceDetail = new InvoiceDetail();
//            invoiceDetail.setRowNo(rowNo);
//            invoiceDetail.setErpCode(orderDetail.getErpCode());
//            invoiceDetail.setErpName(orderDetail.getCommodityName());
//            invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
//            invoiceDetail.setPrice(price);
//            invoiceDetail.setTotalAmount(totalAmount);
//            invoiceDetail.setTaxAmount(taxAmount);
//            invoiceDetail.setTaxRate(taxRate);
//            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
//            invoiceDetail.setInvoiceLineType("Regular_Line");
//            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
//            invoiceDetail.setPolicyStatus("NO");
//            invoiceDetail.setPolicyTaxRate(null);
//            invoiceDetail.setIsValid(1L);
//            invoiceDetail.setCreated(applyTime);
//            invoiceDetail.setUpdated(applyTime);
//            invoiceDetail.setCreatedBy(createdBy);
//            invoiceDetail.setUpdatedBy(createdBy);
//            invoiceDetail.setVersion(1L);
//            invoiceDetails.add(invoiceDetail);
//        }
//
//
//        Preconditions.checkArgument(invoiceAmount.doubleValue()!=sumDetailInvoiceAmount.doubleValue(),"发票头与商品行合计金额不一致");
//        BigDecimal priceTaxAmount=sumDetailInvoiceAmount.add(sumDetailTaxAmount);
//
//        invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
//        invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
//        invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
//        invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
//        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
//        invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
//        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
//        invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
//        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
//        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
//        invoiceMain.setActualPayAmount(invoiceAmount);
//        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
//        invoiceMain.setDeliveryType(deliveryType);
//        invoiceMain.setInvoiceAmount(invoiceAmount);
//        invoiceMain.setTaxAmount(sumDetailTaxAmount);
//        invoiceMain.setPriceTaxAmount(priceTaxAmount);
//        invoiceMain.setSplitBill("NOT");
//        invoiceMain.setOrderCreated(Instant.ofEpochMilli(baseOrderInfo.getCreated().getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
//        invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
//        invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
//        invoiceMain.setIsValid(1L);
//        invoiceMain.setCreated(LocalDateTime.now());
//        invoiceMain.setUpdated(LocalDateTime.now());
//        invoiceMain.setVersion(1L);

//        command.setInvoiceMain(invoiceMain);
//        command.setDetails(invoiceDetails);


        return command;



    }
}
