package com.yxt.invoice.bootstrap;


import com.yxt.domain.order.order_query.req.B2cOrderPageSearchReq;
import com.yxt.domain.order.order_query.req.B2cOrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfoResDto;
import com.yxt.domain.order.order_query.res.B2cOrderListResDto;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.B2cRefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.B2cRefundPageSearchRes;
import com.yxt.domain.order.refund_query.res.B2cRefundSearchByRefundNoRes;
import com.yxt.invoice.application.third.order.feign.B2COrderClient;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class B2cInterfaceTest extends BaseTest {


  @Resource
  private B2COrderClient b2COrderClient;


  @Test
  public void testOrderSearchPage() {
    B2cOrderPageSearchReq req = new B2cOrderPageSearchReq();
    req.setCurrentPage(1L);
    req.setPageSize(100L);
    req.setOrderNo("1780819219909274624");
    ResponseBase<PageDTO<B2cOrderListResDto>> pageDTOResponseBase = b2COrderClient.orderSearchPage(
        req);
    System.out.println();
  }

  @Test
  public void testOrderSearchByOrderNo() {
    B2cOrderSearchByOrderNoReq req = new B2cOrderSearchByOrderNoReq();
    req.setOmsOrderNo("1780819220662152448");
    ResponseBase<B2cOmsOrderInfoResDto> b2cOmsOrderInfoResDtoResponseBase = b2COrderClient.orderSearchByOrderNo(
        req);
    System.out.println();
  }

  @Test
  public void testRefundSearchPage() {
    B2cRefundPageSearchReq req = new B2cRefundPageSearchReq();
    req.setCurrentPage(1L);
    req.setPageSize(100L);
    req.setRefundNo("1841064264463142918");
    ResponseBase<PageDTO<B2cRefundPageSearchRes>> pageDTOResponseBase = b2COrderClient.refundSearchPage(
        req);
    System.out.println();
  }

  @Test
  public void testRefundSearchByRefundNo() {
    B2cRefundSearchByRefundNoReq req = new B2cRefundSearchByRefundNoReq();
    req.setThirdRefundNo("3301740662536519079");
    req.setThirdPlatformCode("27");

    ResponseBase<B2cRefundSearchByRefundNoRes> b2cRefundSearchByRefundNoResResponseBase = b2COrderClient.refundSearchByRefundNo(
        req);
    System.out.println();
  }


}
