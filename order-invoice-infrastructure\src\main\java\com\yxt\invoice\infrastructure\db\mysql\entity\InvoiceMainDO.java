package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 发票主表DO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@TableName("invoice_main")
public class InvoiceMainDO {

  /**
   * 主键ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 机构编码
   */
  private String organizationCode;

  /**
   * 机构名称
   */
  private String organizationName;

  /**
   * 开票单号
   */
  private String invoiceMainNo;

  /**
   * 第三方平台编码
   */
  private String thirdPlatformCode;

  /**
   * 第三方订单号
   */
  private String thirdOrderNo;

  /**
   * 订单号
   */
  private String orderNo;

  /**
   * pos销售单号
   */
  private String posNo;

  /**
   * 会员编号
   */
  private String userId;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 交易渠道
   */
  private String transactionChannel;

  /**
   * 供应商编码
   */
  private String providerCode;

  /**
   * 发票代码
   */
  private String invoiceCode;

  /**
   * 发票号码
   */
  private String invoiceNo;

  /**
   * 红蓝字标识
   */
  private String invoiceRedBlueType;

  /**
   * 红冲发票单号
   */
  private String redInvoiceMainNo;

  /**
   * 红冲原因 01: 开票有误 02: 销货退回 03: 服务中止 04: 销售折让
   */
  private String redInvoiceReason;

  /**
   * 备注
   */
  private String notes;

  /**
   * 发票类型
   */
  private String invoiceType;

  /**
   * 发票状态
   */
  private String invoiceStatus;

  /**
   * 同步状态
   */
  private String syncStatus;

  /**
   * 实付金额
   */
  private BigDecimal actualPayAmount;

  /**
   * 运费
   */
  private BigDecimal deliveryAmount;

  /**
   * 配送方式
   */
  private String deliveryType;

  /**
   * 不含税金额
   */
  private BigDecimal invoiceAmount;

  /**
   * 税额
   */
  private BigDecimal taxAmount;

  /**
   * 价税合计
   */
  private BigDecimal priceTaxAmount;

  /**
   * 是否拆单
   */
  private String splitBill;

  /**
   * 订单创建时间
   */
  private LocalDateTime orderCreated;

  /**
   * PDF地址
   */
  private String pdfUrl;

  /**
   * 开票错误信息
   */
  private String invoiceErrMsg;

  /**
   * 申请时间
   */
  private LocalDateTime applyTime;

  /**
   * 开票完成时间
   */
  private LocalDateTime completeTime;


  /**
   * 操作员
   */
  private String operator;

  /**
   * 收款人
   */
  private String payee;

  /**
   * 复核人
   */
  private String reviewed;

  /**
   * 申请渠道
   */
  private String applyChannel;

  /**
   * 销方编号
   */
  private String sellerNumber;

  /**
   * 销方名称
   */
  private String sellerName;

  /**
   * 销方纳税人识别号
   */
  private String sellerTin;

  /**
   * 销方地址
   */
  private String sellerAddress;

  /**
   * 销方电话
   */
  private String sellerPhone;

  /**
   * 销方开户行
   */
  private String sellerBank;

  /**
   * 销方银行账户
   */
  private String sellerBankAccount;

  /**
   * 购方类型
   */
  private String buyerPartyType;

  /**
   * 购方名称
   */
  private String buyerName;

  /**
   * 购方纳税人识别号
   */
  private String buyerTin;

  /**
   * 购方地址
   */
  private String buyerAddress;

  /**
   * 购方电话
   */
  private String buyerPhone;

  /**
   * 购方开户行
   */
  private String buyerBank;

  /**
   * 购方银行账户
   */
  private String buyerBankAccount;

  /**
   * 购方邮箱
   */
  private String buyerEmail;

  /**
   * 购方手机号
   */
  private String buyerMobile;

  /**
   * 是否显示购方银行账户
   */
  private String showBuyerBankAccount;

  /**
   * 是否有效
   */
  private Long isValid;


  /**
   * 供应商参数 List<ProviderParam></>
   */
  private String providerParam;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT)
  private LocalDateTime created;

  /**
   * 更新时间
   */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本
   */
  @Version
  private Long version;


}
