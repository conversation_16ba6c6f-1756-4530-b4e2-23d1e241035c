package com.yxt.invoice.domain.event.create;

import com.yxt.invoice.domain.event.BaseCreateInvoiceDomainEvent;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.bytebuddy.description.field.FieldDescription.InGenericShape;

/**
 * 发票申请事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceCheckProviderPullEvent extends BaseCreateInvoiceDomainEvent<InvoiceCheckProviderPullEvent.Data> {

    public static final String TYPE = "InvoiceCheckProviderPullEvent";

    public InvoiceCheckProviderPullEvent(InvoiceAggregate aggregate) {
        super(aggregate, null, TYPE,new Data(aggregate));
    }

    @Getter
    @Setter
    @ToString(callSuper = true)
    @NoArgsConstructor
    public static class Data extends BaseCreateInvoiceDomainEvent.BaseData {
        private InvoiceAggregate invoiceAggregate;

        Data(InvoiceAggregate aggregate) {
            super.convert(aggregate);
            this.invoiceAggregate = aggregate;
        }
    }
}
