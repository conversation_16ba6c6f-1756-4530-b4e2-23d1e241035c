package com.yxt.invoice.application.service.impl;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.B2cOrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfoResDto;
import com.yxt.domain.order.order_query.res.OrderDetailResDto;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.B2cRefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.B2cRefundPageSearchRes;
import com.yxt.domain.order.refund_query.res.B2cRefundSearchByRefundNoRes;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.application.service.convert.InvoiceConvert;
import com.yxt.invoice.application.third.baseinfo.feign.BaseInfoClient;
import com.yxt.invoice.application.third.baseinfo.feign.MemberClient;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.B2COrderClient;
import com.yxt.invoice.application.third.order.feign.O2OOrderClient;
import com.yxt.invoice.application.third.order.feign.OfflineOrderServiceClient;
import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.factory.InvoiceAggregateFactory;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.message.InvoiceDomainEventProducer;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.res.org.BizUnitOrgResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.common.base_order_dto.OrderDetail;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.open.sdk.offline_order.req.OfflineOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineRefundOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.manage.IOfflineRefundOrderListReq;
import com.yxt.order.open.sdk.offline_order.res.manage.IOfflineRefundOrderRes;
import com.yxt.order.types.DsConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发票应用服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
@Service
public class InvoiceApplicationServiceImpl implements InvoiceApplicationService {

    @Resource
    private O2OOrderClient o2OOrderClient;
    @Resource
    private B2COrderClient b2COrderClient;
    @Resource
    private OfflineOrderServiceClient offlineOrderServiceClient;
    @Resource
    private MiddleMerchandiseClient middleMerchandiseClient;

    @Resource
    private BaseInfoClient baseInfoClient;
    @Resource
    private MemberClient memberClient;

    @Resource
    private InvoiceRepository invoiceRepository;

    @Resource
    private ProviderInvoiceRepository providerInvoiceRepository;

    @Resource
    private InvoiceDomainEventProducer invoiceDomainEventProducer;

    @Resource
    private InvoiceAggregateFactory invoiceAggregateFactory;


    @Override
    public InvoiceAggregate simpleApplyInvoice(ApplyInvoiceCommand command) {
        String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();
        String transactionChannel = command.getInvoiceMain().getTransactionChannel();
        String businessType = command.getInvoiceMain().getBusinessType();

        InvoiceAggregate invoiceAggregate = invoiceRepository.findByOrderNo(orderNo);
        if (invoiceAggregate != null) {
            return invoiceAggregate;
        }


        if (transactionChannel.equals("offline")) {
            command = extractedByOffline(command);
        } else {
            if (businessType.equals("O2O")) {
                command = extractedByOnlineO2O(command);
            } else {
                command = extractedByOnlineB2C(command);
            }
        }
        try {
            // 命令转模型
            InvoiceAggregate aggravate = invoiceAggregateFactory.createAggravate(command);
            ExistsOrderInvoiceCommand existsOrderInvoiceCommand = aggravate.createExistsOrderInvoiceCommand();
            // 校验是否已申请
            if (invoiceRepository.exists(existsOrderInvoiceCommand)) {
                aggravate.existsApply();
                invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());
                return aggravate;
            }
            // 入库
            invoiceRepository.doSave(aggravate);
            //调用三方申请
            boolean b = providerInvoiceRepository.applyProviderInvoice(aggravate);
            if(!b){
                log.error("applyProviderInvoice error  发票ID：{}, 开票单号：{}",aggravate.getInvoiceMain().getId(), aggravate.getInvoiceMain().getInvoiceMainNo());
            }
            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());

            log.info("申请开票成功，发票ID：{}, 开票单号：{}",aggravate.getInvoiceMain().getId(), aggravate.getInvoiceMain().getInvoiceMainNo());
            return aggravate;
        } catch (Exception e) {
            log.error("申请开票失败，订单号：{}, 错误：{}", command.getInvoiceMain().getOrderNo(), e.getMessage(), e);
            throw e;
        }

    }

    private ApplyInvoiceCommand extractedByOnlineB2C(ApplyInvoiceCommand command) {
        String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();
        String userId = command.getInvoiceMain().getUserId();
        String merCode = command.getInvoiceMain().getMerCode();

        B2cOrderSearchByOrderNoReq req = new B2cOrderSearchByOrderNoReq();
        req.setOmsOrderNo(orderNo);
        ResponseBase<B2cOmsOrderInfoResDto> omsOrderInfoResDtoResponseBase = b2COrderClient.orderSearchByOrderNo(req);
        Preconditions.checkArgument(omsOrderInfoResDtoResponseBase.checkSuccess(), "查询线上单B2C数据异常,暂不支持开票");
        B2cOmsOrderInfoResDto orderInfo = omsOrderInfoResDtoResponseBase.getData();

        Preconditions.checkArgument(orderInfo.getErpStatus() == 100, "订单下账状态,暂不支持开票");
        Preconditions.checkArgument(orderInfo.getOrderStatus() != 101 && orderInfo.getOrderStatus() != 102, "订单状态,暂不支持开票");
        B2cRefundPageSearchReq refundPageSearchReq = new B2cRefundPageSearchReq();
        refundPageSearchReq.setThirdOrderNo(orderInfo.getThirdOrderNo());
        ResponseBase<PageDTO<B2cRefundPageSearchRes>> pageDTOResponseBase = b2COrderClient.refundSearchPage(refundPageSearchReq);
        Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "订单销售流水信息不全,暂不支持开票");


        if (StringUtils.isNotEmpty(orderInfo.getMemberNo())) {
            ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(orderInfo.getMemberNo());
            Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
            Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId), "会员信息不一致,暂不支持开票");
        }
        List<B2cRefundSearchByRefundNoRes> refundDataList = new ArrayList<>();
        List<B2cRefundPageSearchRes> tempRefundList = pageDTOResponseBase.getData().getData();
        for (B2cRefundPageSearchRes dataRefund : tempRefundList) {
            B2cRefundSearchByRefundNoReq refundSearchByRefundNoReq = new B2cRefundSearchByRefundNoReq();
            refundSearchByRefundNoReq.setThirdRefundNo(dataRefund.getThirdRefundNo());
            ResponseBase<B2cRefundSearchByRefundNoRes> refundSearchByRefundNoResResponseBase = b2COrderClient.refundSearchByRefundNo(refundSearchByRefundNoReq);
            Preconditions.checkArgument(refundSearchByRefundNoResResponseBase.checkSuccess(), "查询退款单失败,暂不支持开票");
            B2cRefundSearchByRefundNoRes dataRefundTemp = refundSearchByRefundNoResResponseBase.getData();
            if (Objects.equals(dataRefundTemp.getState(), "102") || Objects.equals(dataRefundTemp.getState(), "103")) {
                continue;
            }
            Preconditions.checkArgument(Objects.equals(dataRefundTemp.getState(), "100"), "退款单状态,暂不支持开票");
            Preconditions.checkArgument(dataRefundTemp.getErpState() == 100, "退款单下账状态,暂不支持开票");
            refundDataList.add(dataRefundTemp);
        }
        String storeCode = orderInfo.getOrganizationCode();
        List<String> erpCodeList = orderInfo.getOrderDetails().stream().map(OrderDetailResDto::getErpCode).distinct().collect(Collectors.toList());
        ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
        Preconditions.checkArgument(detailListPriceResponse.checkSuccess(), "查询商品均价失败,暂不支持开票");
        ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null, storeCode);
        Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
        Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream().filter(d -> "1".equals(d.getLayer())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
        command.getInvoiceMain().setCompanyCode(first.get().getUnitCode());
        command.getInvoiceMain().setCompanyName(first.get().getUnitName());

        return InvoiceConvert.applyInvoiceConvertByOnlineOrderB2C(orderInfo, refundDataList, command, detailListPriceResponse.getData());

    }

    private ApplyInvoiceCommand extractedByOnlineO2O(ApplyInvoiceCommand command) {
        String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();
        String userId = command.getInvoiceMain().getUserId();
        String merCode = command.getInvoiceMain().getMerCode();

        OrderSearchByOrderNoReq req = new OrderSearchByOrderNoReq();
        req.setOrderNo(orderNo);
        ResponseBase<OrderDomainRelatedRes> orderDomainRelatedResResponseBase = o2OOrderClient.orderSearchByOrderNo(req);
        Preconditions.checkArgument(orderDomainRelatedResResponseBase.checkSuccess(), "查询线上单O2O数据异常,暂不支持开票");
        OrderDomainRelatedRes data = orderDomainRelatedResResponseBase.getData();
        OrderInfo orderInfo = data.getOrderInfo();
        Preconditions.checkArgument(orderInfo.getErpState() == 100, "订单下账状态,暂不支持开票");
        Preconditions.checkArgument(orderInfo.getOrderState() != 101 && orderInfo.getOrderState() != 102, "订单状态,暂不支持开票");
        Preconditions.checkArgument(StringUtils.isNotEmpty(orderInfo.getErpSaleNo()), "订单销售流水信息不全,暂不支持开票");
        RefundPageSearchReq refundPageSearchReq = new RefundPageSearchReq();
        refundPageSearchReq.setOrderNoList(Collections.singletonList(orderNo));
        ResponseBase<PageDTO<RefundSimpleRes>> pageDTOResponseBase = o2OOrderClient.refundSearchPage(refundPageSearchReq);
        Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "订单销售流水信息不全,暂不支持开票");


        if (StringUtils.isNotEmpty(orderInfo.getMemberNo())) {
            ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(orderInfo.getMemberNo());
            Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
            Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId), "会员信息不一致,暂不支持开票");
        }
        List<RefundDomainRelatedRes> refundDataList = new ArrayList<>();
        List<RefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
        for (RefundSimpleRes dataRefund : tempRefundList) {
            RefundSearchByRefundNoReq refundSearchByRefundNoReq = new RefundSearchByRefundNoReq();
            refundSearchByRefundNoReq.setRefundNo(dataRefund.getRefundNo());
            ResponseBase<RefundDomainRelatedRes> refundDomainRelatedResResponseBase = o2OOrderClient.refundSearchByRefundNo(refundSearchByRefundNoReq);
            Preconditions.checkArgument(refundDomainRelatedResResponseBase.checkSuccess(), "查询退款单失败,暂不支持开票");
            RefundDomainRelatedRes dataRefundTemp = refundDomainRelatedResResponseBase.getData();
            if (dataRefundTemp.getRefundOrder().getState() == 102 || dataRefundTemp.getRefundOrder().getState() == 103) {
                continue;
            }
            Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getState() == 100, "退款单状态,暂不支持开票");
            Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getErpState() == 100, "退款单下账状态,暂不支持开票");
            refundDataList.add(dataRefundTemp);
        }
        String storeCode = orderInfo.getOrganizationCode();
        List<String> erpCodeList = data.getDetailList().stream().map(OrderDetail::getErpCode).distinct().collect(Collectors.toList());
        ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
        Preconditions.checkArgument(detailListPriceResponse.checkSuccess(), "查询商品均价失败,暂不支持开票");
        ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null, storeCode);
        Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
        Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream().filter(d -> "1".equals(d.getLayer())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
        command.getInvoiceMain().setCompanyCode(first.get().getUnitCode());
        command.getInvoiceMain().setCompanyName(first.get().getUnitName());

        return InvoiceConvert.applyInvoiceConvertByOnlineOrderO2O(data, refundDataList, command, detailListPriceResponse.getData());
    }

    private ApplyInvoiceCommand extractedByOffline(ApplyInvoiceCommand command) {

        String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();
        String userId = command.getInvoiceMain().getUserId();
        String merCode = command.getInvoiceMain().getMerCode();

        OfflineOrderDetailQueryReqDto queryReqDto = new OfflineOrderDetailQueryReqDto();
        queryReqDto.setOrderNo(orderNo);
        //查询线下单补充
        ResponseBase<OfflineOrderModel> offlineOrderModelResponseBase = offlineOrderServiceClient.detail(queryReqDto);
        Preconditions.checkArgument(offlineOrderModelResponseBase.checkSuccess(), "查询线下单数据异常,暂不支持开票");

        OfflineOrderModel data = offlineOrderModelResponseBase.getData();
        if (null != data.getBaseUserInfo().getUserId() && StringUtils.isNotEmpty(userId)) {
            Preconditions.checkArgument(data.getBaseUserInfo().getUserId().getUserId().equals(userId), "此订单为其他会员所有,请核对信息申请");
        }
        IOfflineRefundOrderListReq offlineRefundOrderListReq = new IOfflineRefundOrderListReq();
        offlineRefundOrderListReq.setOrderNo(orderNo);
        ResponseBase<PageDTO<IOfflineRefundOrderRes>> pageDTOResponseBase = offlineOrderServiceClient.offlineRefundOrderList(offlineRefundOrderListReq);
        Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "查询线下单退单数据异常,暂不支持开票");

        List<IOfflineRefundOrderRes> refundDataTempList = pageDTOResponseBase.getData().getData();
        List<OfflineRefundOrderModel> refundDataList = new ArrayList<>();

        for (IOfflineRefundOrderRes iOfflineRefundOrderRes : refundDataTempList) {
            OfflineRefundOrderDetailQueryReqDto refundDetailQueryReqDto = new OfflineRefundOrderDetailQueryReqDto();
            refundDetailQueryReqDto.setRefundNo(iOfflineRefundOrderRes.getRefundNo());
            ResponseBase<OfflineRefundOrderModel> offlineRefundOrderModelResponseBase = offlineOrderServiceClient.refundDetail(refundDetailQueryReqDto);
            Preconditions.checkArgument(offlineRefundOrderModelResponseBase.checkSuccess(), "查询线下单退单数据异常,暂不支持开票");
            Preconditions.checkArgument(!offlineRefundOrderModelResponseBase.getData().getBaseRefundInfo().getRefundTypeValue().equals("ALL"), "暂不支持整单退开票");
            refundDataList.add(offlineRefundOrderModelResponseBase.getData());
        }
        String storeCode = data.getBaseOrganizationInfo().getStoreCode();
        List<String> erpCodeList = data.getOrderDetailList().stream().map(d -> d.getBaseOrderDetailInfo().getErpCode().getErpCode()).distinct().collect(Collectors.toList());
        ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
        Preconditions.checkArgument(detailListPriceResponse.checkSuccess(), "查询商品平均价失败,暂不支持开票");

        return InvoiceConvert.applyInvoiceConvertByOfflineOrder(data, refundDataList, command, detailListPriceResponse.getData());
    }


    @Override
    public InvoiceAggregate simpleRedCreditInvoice(RedCreditInvoiceCommand command) {
        log.info("开始红冲发票，开票单号：{}, 操作人ID：{}", command.getInvoiceMainNo(), command.getOperatorUserId());

        try {

            // 1. 查询发票聚合根
            InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(command.getInvoiceMainNo());
            Preconditions.checkArgument(aggregate != null, "开票单不存在");
            Preconditions.checkArgument(aggregate.isAllowRedInvoice(), "非蓝票且开具成功发票,不允许红冲");
            InvoiceAggregate redCreditInvoiceAggregate = invoiceAggregateFactory.redCreditAggregate(aggregate, command);
            // 4. 保存更新
            redCreditInvoiceAggregate = invoiceRepository.doSaveRedInvoice(aggregate, redCreditInvoiceAggregate);
            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(redCreditInvoiceAggregate.getDomainEvents());

            log.info("红冲发票成功，开票单号：{}", redCreditInvoiceAggregate.getInvoiceMain().getInvoiceMainNo());

            // 6. 返回聚合根
            return redCreditInvoiceAggregate;

        } catch (Exception e) {
            log.error("红冲发票失败，开票单号：{}, 错误：{}", command.getInvoiceMainNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，由控制器处理
        }
    }


    @Override
    public ExistsOrderInvoice queryOrderExistsInvoice(ExistsOrderInvoiceCommand command) {
        return null;
    }

    @Override
    public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command) {
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public PageDTO<InvoiceMain> queryInvoiceList(QueryInvoiceListCommand query) {
        try {
            return invoiceRepository.findInvoiceManByConditions(query);
        } catch (Exception e) {
            log.error("查询发票列表失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
        log.info("查询发票详情，开票单号：{}", query.getInvoiceMainNo());
        try {
            // 查询发票聚合根
            InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(query.getInvoiceMainNo());
           Preconditions.checkArgument(aggregate != null, "开票单号不存在");
            return aggregate;
        } catch (Exception e) {
            log.error("查询发票详情失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }



}
