package com.yxt.invoice.interfaces.mq.rabbit;

import com.rabbitmq.client.Channel;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 发票相关RabbitMQ消息监听器
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Component
@Slf4j
public class InvoiceRabbitMessageConsumer {

  @RabbitListener(queues = "broadcast_queue_B2C",containerFactory = "multiRabbitListenerContainerFactory")
  @RabbitHandler
  public void receiveBroadcastB2C(Message message, Channel channel) throws IOException {

    try {
      // 处理接收到的广播消息
      System.out.println("队列1接收到广播消息B2C: " + new String(message.getBody()));

      // todo 待获取到消息时再具体对接
      // 根据responseId来检索看表中有没有数据，入股有则不处理，如果没有走则入库逻辑


      //ACK,确认一条消息已经被消费
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (Exception e) {
      //NACK basicNack(deliveryTag, multiple, requeue)
      channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
      throw new RuntimeException(e);
    }
  }
}
