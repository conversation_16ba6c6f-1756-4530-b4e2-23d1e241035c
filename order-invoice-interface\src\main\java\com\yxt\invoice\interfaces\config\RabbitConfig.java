package com.yxt.invoice.interfaces.config;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Configuration
public class RabbitConfig {

  /**
   * 消息转换器 - 使用JSON格式
   */
  @Bean
  public MessageConverter messageConverter() {
    return new Jackson2JsonMessageConverter();
  }

  /**
   * RabbitTemplate配置
   */
  @Bean
  public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
    RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
    rabbitTemplate.setMessageConverter(messageConverter());
    return rabbitTemplate;
  }

  /**
   * 监听器容器工厂配置
   */
  @Bean("multiRabbitListenerContainerFactory")
  public RabbitListenerContainerFactory<?> rabbitListenerContainerFactory(
      ConnectionFactory connectionFactory) {
    SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
    factory.setConnectionFactory(connectionFactory);
    factory.setMessageConverter(messageConverter());
    // 设置手动确认模式
    factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
    // 设置并发消费者数量
    factory.setConcurrentConsumers(1);
    factory.setMaxConcurrentConsumers(10);
    return factory;
  }

  /**
   * 发票交换机
   */
  @Bean("invoiceQueue")
  public Queue invoiceQueue() {
    return new Queue("broadcast_queue_B2C", true);
  }


  @Bean("invoiceFanoutExchange")
  public FanoutExchange invoiceFanoutExchange() {
    return new FanoutExchange("broadcast_exchange");
  }

  @Bean
  public Binding broadcastBindingHaiDian(@Qualifier("invoiceQueue") Queue queue,
      @Qualifier("invoiceFanoutExchange") FanoutExchange fanoutExchange) {
    return BindingBuilder.bind(queue).to(fanoutExchange);
  }

}
