package com.yxt.invoice.application.service.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfoResDto;
import com.yxt.domain.order.order_query.res.ErpBillInfoResDto;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderPayInfoResDto;
import com.yxt.domain.order.refund_query.res.B2cRefundSearchByRefundNoRes;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.types.offline.enums.CouponTypeEnum;
import com.yxt.order.types.offline.enums.DataDimensionTypeEnum;
import com.yxt.order.types.order.enums.PlatformCodeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class InvoiceAmountConvert {

    public static List<InvoiceAmount> convertByOfflineOrder(OfflineOrderModel data, List<OfflineRefundOrderModel> refundDataList) {
        List<InvoiceAmount> invoiceAmounts = new ArrayList<>();
        BigDecimal actualPayAmount = data.getBaseOrderInfo().getActualPayAmount();

        List<OfflineOrderModel.OrderCouponData> collect = data.getOrderCouponList().stream().filter(d -> d.getType().equals(DataDimensionTypeEnum.ORDER.name()) && d.getCouponType().equals(CouponTypeEnum.XY_CASH.name())).collect(Collectors.toList());

        BigDecimal xy_cash = BigDecimal.ZERO;
        if (!collect.isEmpty()) {
            for (OfflineOrderModel.OrderCouponData orderCouponData : collect) {
                xy_cash = xy_cash.add(orderCouponData.getUsedCouponAmount());
            }
        }
        BigDecimal subtract = actualPayAmount.subtract(xy_cash);
        for (OfflineRefundOrderModel offlineRefundOrderModel : refundDataList) {
            subtract = subtract.subtract(offlineRefundOrderModel.getBaseRefundInfo().getConsumerRefund());
        }
        invoiceAmounts.add(new InvoiceAmount("invoiceAmount", subtract.toPlainString()));
        return invoiceAmounts;
    }


    public static List<InvoiceAmount> convertByOnlineOrderO2O(OrderDomainRelatedRes data, List<RefundDomainRelatedRes> refundDataList) {
        List<InvoiceAmount> invoiceAmounts = new ArrayList<>();
        String deliveryType = data.getOrderDeliveryRecord().getDeliveryType();
        BigDecimal buyerActualAmount = data.getOrderPayInfo().getBuyerActualAmount();

        BigDecimal xy_cash = BigDecimal.ZERO;
        // 新增现金券金额
        List<OrderPayInfo.PaySaleInfo> paySaleInfos = JSON.parseArray(data.getOrderPayInfo().getPaySaleInfo(), OrderPayInfo.PaySaleInfo.class);
        if (CollUtil.isNotEmpty(paySaleInfos)) {
            xy_cash = paySaleInfos.stream().map(OrderPayInfo.PaySaleInfo::getSaleAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        }

        BigDecimal subtract = buyerActualAmount.subtract(xy_cash);
        for (RefundDomainRelatedRes refundDomainRelatedRes : refundDataList) {
            subtract = subtract.subtract(refundDomainRelatedRes.getRefundOrder().getConsumerRefund());
        }

        invoiceAmounts.add(new InvoiceAmount("invoiceAmount", subtract.toPlainString()));
        if (deliveryType.equals("3")) {
            BigDecimal invoiceAmountWithPostFee = subtract.add(data.getOrderPayInfo().getDeliveryFee().subtract(data.getOrderPayInfo().getPostFeeDiscount()));
            BigDecimal invoiceAmountWithPostFeeWithSubsidy = invoiceAmountWithPostFee.add(data.getOrderPayInfo().getPlatformDiscount());
            if (data.getOrderInfo().getThirdPlatformCode().equals(PlatformCodeEnum.JD_DAOJIA.getCode())) {
                invoiceAmountWithPostFeeWithSubsidy = data.getErpBillInfo().getBillTotalAmount();
            }
            invoiceAmounts.add(new InvoiceAmount("invoiceAmountWithPostFee", invoiceAmountWithPostFee.toPlainString()));
            invoiceAmounts.add(new InvoiceAmount("invoiceAmountWithPostFeeWithSubsidy", invoiceAmountWithPostFeeWithSubsidy.toPlainString()));
        }
        if (deliveryType.equals("1") || deliveryType.equals("2")) {
            BigDecimal invoiceAmountWithSubsidy = subtract.add(data.getOrderPayInfo().getPlatformDiscount());
            invoiceAmounts.add(new InvoiceAmount("invoiceAmountWithSubsidy", invoiceAmountWithSubsidy.toPlainString()));
        }
        return invoiceAmounts;
    }

    public static List<InvoiceAmount> convertByOnlineOrderB2C(B2cOmsOrderInfoResDto data, List<B2cRefundSearchByRefundNoRes> refundDataList) {
        OrderPayInfoResDto orderPayInfo = data.getOrderPayInfo();
        ErpBillInfoResDto erpBillInfo = data.getErpBillInfo();
        BigDecimal buyerActualAmount = orderPayInfo.getBuyerActualAmount();


        BigDecimal xy_cash = BigDecimal.ZERO;
        // 新增现金券金额
        if (null != orderPayInfo.getAllCashCouponAmount()) {
            xy_cash = orderPayInfo.getAllCashCouponAmount();
        }
        BigDecimal subtract = buyerActualAmount.subtract(xy_cash);
        for (B2cRefundSearchByRefundNoRes refundDetail : refundDataList) {
            subtract=subtract.subtract(refundDetail.getConsumerRefund());
        }


        List<InvoiceAmount> invoiceAmounts = new ArrayList<>();

        invoiceAmounts.add(new InvoiceAmount("invoiceAmount", buyerActualAmount.toPlainString()));
        BigDecimal invoiceAmountWithPostFee = buyerActualAmount.add(orderPayInfo.getDeliveryFee().subtract(orderPayInfo.getPostFeeDiscount()));
        BigDecimal invoiceAmountWithPostFeeWithSubsidy = invoiceAmountWithPostFee.add(orderPayInfo.getPlatformDiscount());
        if (data.getThirdPlatformCode().equals(PlatformCodeEnum.JD_DAOJIA.getCode())) {
            invoiceAmountWithPostFeeWithSubsidy = erpBillInfo.getBillTotalAmount();
        }
        invoiceAmounts.add(new InvoiceAmount("invoiceAmountWithPostFee", invoiceAmountWithPostFee.toPlainString()));
        invoiceAmounts.add(new InvoiceAmount("invoiceAmountWithPostFeeWithSubsidy", invoiceAmountWithPostFeeWithSubsidy.toPlainString()));

        return invoiceAmounts;
    }
}
