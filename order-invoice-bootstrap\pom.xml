<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.ddd</groupId>
    <artifactId>order-invoice-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.yxt.ddd.service.starter</groupId>
  <artifactId>order-invoice-bootstrap</artifactId>
  <packaging>jar</packaging>
  <version>1.0.0</version>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <jar.name>order-invoice-service</jar.name>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.ddd.interfaces</groupId>
      <artifactId>order-invoice-interface</artifactId>
      <version>1.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.yxt.ddd.infrastructure</groupId>
      <artifactId>order-invoice-infrastructure</artifactId>
      <version>1.0.0</version>
    </dependency>


  </dependencies>

  <build>
    <defaultGoal>compile</defaultGoal>
    <finalName>${jar.name}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!--打包jar的存放路径-->
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>copy</id>
            <phase>package</phase>
            <configuration>
              <target>
                <delete file="../target/${jar.name}.jar"/>
                <copy todir="../target">
                  <fileset dir="${project.build.directory}">
                    <include name="${jar.name}.jar"/>
                  </fileset>
                </copy>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>