<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.ddd</groupId>
    <artifactId>order-invoice-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.yxt.ddd.interfaces</groupId>
  <artifactId>order-invoice-interface</artifactId>
  <version>1.0.0</version>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <yxt.order-types.version>2.5.1-SNAPSHOT</yxt.order-types.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.ddd.application</groupId>
      <artifactId>order-invoice-application</artifactId>
      <version>1.0.0</version>
    </dependency>
    <!--放这里依赖的原因是1.项目内部是基于feign调用,不需要从底层开始封装 2.作为流量入口,放到interface层.(从DDD概念来说,sdk不应该被项目内部的任何模块依赖)-->
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>order-invoice-sdk</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>${yxt.order-types.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
      <version>2.2.6.RELEASE</version>
    </dependency>

    <!-- RabbitMQ -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>

  </dependencies>

</project>