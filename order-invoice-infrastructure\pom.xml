<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.ddd</groupId>
    <artifactId>order-invoice-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.yxt.ddd.infrastructure</groupId>
  <artifactId>order-invoice-infrastructure</artifactId>
  <version>1.0.0</version>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <org.mapstruct.version>1.5.0.RC1</org.mapstruct.version>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <hippo4j.version>1.4.3</hippo4j.version>
    <spring-amqp.version>2.2.0.RELEASE</spring-amqp.version>
    <mybatis-plus.version>3.1.2</mybatis-plus.version>
    <druid.version>1.1.21</druid.version>
    <yxt.order-types.version>2.5.1-SNAPSHOT</yxt.order-types.version>
  </properties>

  <dependencies>
    <!--实现领域的接口-->
    <dependency>
      <groupId>com.yxt.ddd.domain</groupId>
      <artifactId>order-invoice-domain</artifactId>
      <version>1.0.0</version>
    </dependency>
    <!--订单域依赖-->
    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>${yxt.order-types.version}</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.common</groupId>
      <artifactId>order-common</artifactId>
      <version>${yxt.order-types.version}</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <version>3.3.2</version>
    </dependency>
    <!-- MyBatis-Plus -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>
    <!-- MyBatis-Plus Generator -->
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-typehandlers-jsr310</artifactId>
      <version>1.0.2</version>
    </dependency>
    <!-- 数据源 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>${druid.version}</version>
    </dependency>

    <!-- 数据库驱动 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>

    <!-- es -->
    <dependency>
      <groupId>org.elasticsearch.client</groupId>
      <artifactId>elasticsearch-rest-high-level-client</artifactId>
      <version>7.14.0</version>
    </dependency>
    <dependency>
      <groupId>org.elasticsearch</groupId>
      <artifactId>elasticsearch</artifactId>
      <version>7.14.0</version>
    </dependency>
    <dependency>
      <groupId>org.dromara.easy-es</groupId>
      <artifactId>easy-es-boot-starter</artifactId>
      <version>2.0.0</version>
    </dependency>

    <!-- O2O -->
    <dependency>
      <groupId>com.yxt.domain.order</groupId>
      <artifactId>order-domain-sdk</artifactId>
      <version>1.3.5-RELEASE</version>
    </dependency>
    <!-- B2C -->
    <dependency>
    <groupId>com.yxt.domain.order</groupId>
    <artifactId>order-domain-b2c-sdk</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!-- 线下单 -->
    <dependency>
      <groupId>com.yxt.order.open.sdk</groupId>
      <artifactId>order-open-sdk</artifactId>
      <version>2.2.0-RELEASE</version>
    </dependency>
    <!-- 组织 -->
    <dependency>
      <groupId>com.yxt.middle</groupId>
      <artifactId>yxt-middle-baseinfo-sdk</artifactId>
      <version>1.27.5-RELEASE</version>
    </dependency>
    <!-- 会员 -->
    <dependency>
      <groupId>cn.hydee.ydjia</groupId>
      <artifactId>hydee-middle-member-sdk</artifactId>
      <version>1.2.3-RELEASE</version>
    </dependency>
    <!--通用能力-->
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-logic</artifactId>
      <version>4.10.0</version>
    </dependency>

  </dependencies>

</project>