package com.yxt.invoice.infrastructure.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.yxt.invoice.infrastructure.common.UserContext;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceLogDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceLogMapper;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class LogUtils {


  public static void log(String invoiceMainNo, String desc) {
    InvoiceLogMapper invoiceLogMapper = SpringUtil.getBean(InvoiceLogMapper.class);
    if (Objects.isNull(invoiceLogMapper)) {
      log.warn("日志存储未初始化");
      return;
    }
    String userId = UserContext.getCurrentUserId();
    String userName = UserContext.getCurrentUserName();

    InvoiceLogDO invoiceLogDO = new InvoiceLogDO();
    invoiceLogDO.setInvoiceMainNo(invoiceMainNo);
    invoiceLogDO.setOperatorId(StringUtils.isNotEmpty(userId) ? userId : "system");
    invoiceLogDO.setOperatorName(StringUtils.isNotEmpty(userName) ? userName : "system");
    invoiceLogDO.setOperateDesc(desc);
    invoiceLogDO.setCreateTime(LocalDateTime.now());
    invoiceLogMapper.insert(invoiceLogDO);
  }

}
