package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.interfaces.converter.InvoiceDTOConverter;
import com.yxt.invoice.interfaces.converter.InvoiceRequestConverter;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.api.InvoiceApi;
import com.yxt.invoice.sdk.api.InvoiceQueryApi;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.*;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceResDto;
import com.yxt.invoice.sdk.dto.res.ApplyRedCreditResDto;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票控制器
 * 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/api/1.0/invoices")
@Validated
@Slf4j
public class InvoiceController implements InvoiceApi, InvoiceQueryApi {

    @Resource
    private InvoiceService invoiceService;


    @PostMapping("/simpleApplyInvoice")
    @Override
    public ResponseBase<ApplyInvoiceResDto> simpleApplyInvoice(SimpleApplyInvoiceMainReqDto reqDto) {
        log.info("收到申请开票请求， 订单号：{}", reqDto.getOrderNo());

        try {
            // 转换为Command
            ApplyInvoiceCommand command = InvoiceRequestConverter.convertToSimpleApplyInvoiceCommand(reqDto);

            // 调用Service层，返回聚合根
            InvoiceAggregate aggregate = invoiceService.simpleApplyInvoice(command);

            // 转换聚合根为响应DTO
            ApplyInvoiceResDto result = InvoiceDTOConverter.convertToApplyInvoiceResDto(aggregate);

            log.info("申请开票成功，开票单号：{}", result.getInvoiceMain().getInvoiceMainNo());
            return ResponseBase.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("申请开票参数错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_PARAM", e.getMessage());

        } catch (IllegalStateException e) {
            log.warn("申请开票状态错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_STATE", e.getMessage());

        } catch (Exception e) {
            log.error("申请开票系统错误：{}", e.getMessage(), e);
            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
        }
    }

    @PostMapping("/simpleRedApplyInvoice")
    @Override
    public ResponseBase<ApplyRedCreditResDto> simpleApplyRedInvoice(SimpleApplyRedInvoiceMainReqDto reqDto) {

        log.info("收到发票红冲请求，开票单号：{}, 操作人ID：{}", reqDto.getRedInvoiceMainNo(), reqDto.getOperatorUserId());

        try {
            // 转换为Command
            RedCreditInvoiceCommand command = InvoiceRequestConverter.convertToSimpleRedCreditCommand(reqDto);

            // 调用Service层，返回聚合根
            InvoiceAggregate aggregate = invoiceService.simpleRedCreditInvoice(command);

            // 转换聚合根为响应DTO
            ApplyRedCreditResDto result = InvoiceDTOConverter.convertToApplyRedCreditResDto(aggregate);

            log.info("发票红冲成功，开票单号：{}", result.getInvoiceMainNo());
            return ResponseBase.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("发票红冲参数错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_PARAM", e.getMessage());

        } catch (IllegalStateException e) {
            log.warn("发票红冲状态错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_STATE", e.getMessage());

        } catch (Exception e) {
            log.error("发票红冲系统错误：{}", e.getMessage(), e);
            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
        }
    }


    /**
     * 查询发票列表
     */
    @PostMapping("/list")
    @Override
    public ResponseBase<PageDTO<InvoiceMainDTO>> queryInvoiceList(@Valid @RequestBody InvoiceListReqDto request) {
        log.info("收到发票列表查询请求，机构编码：{}, 状态：{}",
                request.getOrganizationCode(), request.getInvoiceStatus());

        try {
            // 转换为Query
            QueryInvoiceListCommand query = InvoiceRequestConverter.convertToInvoiceListQuery(request);

            // 调用Service层，返回聚合根列表
            PageDTO<InvoiceMain> pageDTO = invoiceService.queryInvoiceList(query);

            // 转换为DTO列表
            List<InvoiceMainDTO> dtoList = pageDTO.getData().stream()
                    .map(InvoiceDTOConverter::convertToInvoiceMainDTO)
                    .collect(Collectors.toList());

            // 构建分页响应（这里简化处理，实际应该从分页信息中获取）
            PageDTO<InvoiceMainDTO> pageData = new PageDTO<>(query.getCurrentPage(), query.getPageSize());
            pageData.setTotalCount(pageDTO.getTotalCount());
            pageData.setTotalPage(pageDTO.getTotalPage());
            pageData.setData(dtoList);

            log.info("发票列表查询成功，返回{}条记录", dtoList.size());
            return ResponseBase.success(pageData);

        } catch (IllegalArgumentException e) {
            log.warn("发票列表查询参数错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_PARAM", e.getMessage());

        } catch (Exception e) {
            log.error("发票列表查询系统错误：{}", e.getMessage(), e);
            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
        }
    }

    /**
     * 查询发票详情
     */
    @PostMapping("/detail")
    @Override
    public ResponseBase<InvoiceDetailResponse> queryInvoiceDetail(@Valid @RequestBody InvoiceDetailReqDto req) {
        log.info("收到发票详情查询请求，开票单号：{}",
                req.getInvoiceMainNo());

        try {
            // 转换为Query
            QueryInvoiceDetailCommand command = InvoiceRequestConverter.convertToInvoiceDetailCommand(req);

            // 调用Service层，返回聚合根
            InvoiceAggregate aggregate = invoiceService.queryInvoiceDetail(command);

            // 转换聚合根为响应DTO
            InvoiceDetailResponse result = InvoiceDTOConverter.convertToInvoiceDetailResponse(aggregate);

            log.info("发票详情查询成功，开票单号：{}", result.getInvoiceMain().getInvoiceMainNo());
            return ResponseBase.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("发票详情查询参数错误：{}", e.getMessage());
            return ResponseBase.fail("INVALID_PARAM", e.getMessage());

        } catch (Exception e) {
            log.error("发票详情查询系统错误：{}", e.getMessage(), e);
            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
        }
    }

    @Override
    public ResponseBase<QueryOrderExistsInvoiceResDto> queryOrderExistsInvoice(QueryOrderExistsInvoiceReqDto req) {
        ExistsOrderInvoiceCommand command = InvoiceRequestConverter.convertToExistsOrderInvoiceCommand(req);
        ExistsOrderInvoice existsInvoice = invoiceService.queryOrderExistsInvoice(command);

        QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(existsInvoice);
        return ResponseBase.success(resDto);
    }

    @Override
    public ResponseBase<QueryOrderExistsInvoiceResDto> queryThirdOrderExistsInvoiceReqDto(QueryThirdOrderExistsInvoiceReqDto req) {
        ExistsThirdOrderInvoiceCommand command = InvoiceRequestConverter.convertToExistsThirdOrderInvoiceCommand(req);
        ExistsOrderInvoice existsInvoice = invoiceService.queryThirdOrderExistsInvoiceReqDto(command);

        QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(existsInvoice);
        return ResponseBase.success(resDto);
    }


//    @PostMapping("/applyInvoice")
//    @Override
//    public ResponseBase<ApplyInvoiceResDto> applyInvoice(@Valid @RequestBody ApplyInvoiceReqDto reqDto) {
//        log.info("收到申请开票请求，平台订单号：{}, 订单号：{}", reqDto.getInvoiceMain().getThirdOrderNo(), reqDto.getInvoiceMain().getOrderNo());
//
//        try {
//            // 转换为Command
//            ApplyInvoiceCommand command = InvoiceRequestConverter.convertToApplyInvoiceCommand(reqDto);
//
//            // 调用Service层，返回聚合根
//            InvoiceAggregate aggregate = invoiceService.applyInvoice(command);
//
//            // 转换聚合根为响应DTO
//            ApplyInvoiceResDto result = InvoiceDTOConverter.convertToApplyInvoiceResDto(aggregate);
//
//            log.info("申请开票成功，开票单号：{}", result.getInvoiceMain().getInvoiceMainNo());
//            return ResponseBase.success(result);
//
//        } catch (IllegalArgumentException e) {
//            log.warn("申请开票参数错误：{}", e.getMessage());
//            return ResponseBase.fail("INVALID_PARAM", e.getMessage());
//
//        } catch (IllegalStateException e) {
//            log.warn("申请开票状态错误：{}", e.getMessage());
//            return ResponseBase.fail("INVALID_STATE", e.getMessage());
//
//        } catch (Exception e) {
//            log.error("申请开票系统错误：{}", e.getMessage(), e);
//            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
//        }
//    }


}
