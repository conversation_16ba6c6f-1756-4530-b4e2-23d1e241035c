package com.yxt.lang.exception;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 异常配置属性类
 * 临时创建以解决启动问题
 */
@Component
@ConfigurationProperties(prefix = "exception")
public class ExceptionProperties {
    
    private boolean enabled = true;
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
